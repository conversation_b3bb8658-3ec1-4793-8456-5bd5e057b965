# ProcessFlow

> **⚠️ Development Status**: This package is currently in active development and is not yet ready for production use. The API may change significantly before the first stable release.

A powerful Python framework for building and executing complex data processing workflows with automatic dependency resolution, intelligent parameter management, and comprehensive visualization capabilities.

## Vision & Goals

ProcessFlow aims to revolutionize how data scientists and engineers build complex analytical workflows by providing:

- **Declarative Task Definition**: Define what your tasks do, not how they should be executed
- **Intelligent Dependency Management**: Automatic resolution of task dependencies based on inputs/outputs
- **Flexible Parameter Systems**: Support for external configuration, optional parameters, and runtime overrides
- **Rich Visualization**: Generate comprehensive graphs showing data flow and task relationships
- **Pandas-First Design**: Native integration with pandas DataFrames for seamless data processing
- **Extensible Architecture**: Built to support multiple execution engines and data backends

## Core Architecture

ProcessFlow is built around three main components:

### 1. **Decorators** - The Task Definition Layer
- `@calctask`: Define computational tasks with automatic dependency inference
- `@calctask_transformer`: Transform and rename task inputs/outputs for reusability
- `@input_registry`: Manage external parameters and configuration data
- `@input_registry_asset`: Load parameters from configuration files (TOML, YAML)

### 2. **Execution Engine** - The Process Flow Runner
- `PandasProcessFlow`: Execute workflows on pandas DataFrames
- Automatic dependency resolution and topological sorting
- Intelligent parameter injection and optional argument handling
- Result collection and database extension

### 3. **Visualization & Analysis** - The Graph Builder
- `PandasProcessFlowGraph`: Generate visual representations of workflows
- NetworkX-based graph construction with rich metadata
- Graphviz integration for publication-quality diagrams
- Color-coded node types (tasks, inputs, outputs, parameters)

## Installation

```bash
pip install processflow  # Coming soon
```

## Quick Start

```python
import pandas as pd
from processflow import PandasProcessFlow, calctask

# Define calculation tasks with automatic dependency inference
@calctask("sum")
def add_columns(a: int, b: int, multiplier: int = 10) -> int:
    """Add columns with optional multiplier."""
    return a + b * multiplier

@calctask(["product", "ratio"])
def calculate_metrics(a: int, sum: int, factor: int = 5) -> tuple[int, float]:
    """Calculate product and ratio metrics."""
    return (a * sum), (sum / (a + factor))

@calctask("final_score")
def compute_final(product: int, ratio: float, weight: float = 0.5) -> float:
    """Compute weighted final score."""
    return product * weight + ratio * (1 - weight)

# Create sample data
df = pd.DataFrame({
    "a": [1, 2, 3, 4],
    "b": [10, 20, 30, 40]
})

# Configure optional parameters
optionals = {
    "add_columns": {"multiplier": 100},
    "calculate_metrics": {"factor": 10}
}

# Create and execute workflow
workflow = PandasProcessFlow(
    database=df,
    calctasks=[add_columns, calculate_metrics, compute_final],
    optionals=optionals,
    process_name="sample_workflow"
)

# Execute the workflow
workflow.run()

# Access results
print(workflow.database)
```

## Advanced Features

### External Parameter Management

ProcessFlow provides sophisticated parameter management through the registry system:

```python
from dataclasses import dataclass
from processflow import calctask, input_registry_asset

# Define parameter schema
@input_registry_asset
@dataclass
class ModelConfig:
    learning_rate: float
    batch_size: int
    epochs: int
    regularization: float

# Use parameters in tasks
@calctask("trained_model", params_from=ModelConfig)
def train_model(
    features: pd.DataFrame,
    target: pd.Series,
    learning_rate: float,
    batch_size: int,
    epochs: int,
    regularization: float
) -> object:
    """Train model with external configuration."""
    # Training logic here
    pass
```

### Task Transformation and Reusability

Transform existing tasks for different contexts:

```python
from processflow import calctask_transformer

# Original task
@calctask("revenue")
def calculate_revenue(price: float, quantity: int) -> float:
    return price * quantity

# Transform for different column names
transformed_task = calctask_transformer(
    calculate_revenue,
    meta={"price": "unit_price", "quantity": "units_sold"}
)
```

### Workflow Visualization

Generate comprehensive workflow diagrams:

```python
from processflow import PandasProcessFlowGraph

# Create workflow graph
graph = PandasProcessFlowGraph(workflow)
diagram = graph.build(size=12, legend=True)

# Render to file
diagram.render("my_workflow", format="png", view=True)
```
