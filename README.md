# ProcessFlow

A Python package for building and executing data processing workflows with automatic dependency resolution and graph visualization.

## Overview

ProcessFlow allows you to define data processing tasks using decorators and automatically manages their execution order based on input/output dependencies. It's particularly useful for complex data transformations where tasks depend on the results of other tasks.

## Key Features

- **Automatic Dependency Resolution**: Define tasks with inputs and outputs, and ProcessFlow automatically determines the execution order
- **Pandas Integration**: Built-in support for pandas DataFrames with seamless data flow between tasks
- **Flexible Task Definition**: Use decorators to define calculation tasks with clear input/output specifications
- **Graph Visualization**: Generate visual representations of your process flow
- **Optional Parameters**: Support for task-specific and global optional parameters

## Installation

```bash
pip install processflow
```

## Quick Start

```python
import pandas as pd
from processflow import PandasProcessFlow, calctask

# Define your calculation tasks
@calctask("sum")
def add_columns(a: int, b: int, c: int = 10) -> int:
    return a + b * c

@calctask(["mul", "div"])
def calculate_stats(a: int, sum: int, c: int = 10, d: int = 5) -> tuple[int, float]:
    return (a - c) * sum, a / (sum + d)

@calctask("result")
def final_calculation(mul: int, div: float, z: float = 0.5) -> int:
    return (mul - div) * z

# Create your data
df = pd.DataFrame({"a": [1, 2, 3], "b": [4, 5, 6]})

# Set up optional parameters
optionals = {"add_columns": {"c": 100}}

# Create and run the process flow
ppf = PandasProcessFlow(
    database=df,
    calctasks=[add_columns, calculate_stats, final_calculation],
    optionals=optionals,
    process_name="my_process"
)

# Execute the workflow
ppf.run()
```

## Core Concepts

### CalcTasks
Tasks are defined using the `@calctask` decorator, which specifies:
- **outputs**: The name(s) of the output columns/variables
- **inputs**: Automatically inferred from function parameters
- **optional parameters**: Default values and task-specific overrides

### Process Flow
The `PandasProcessFlow` class manages:
- Task execution order based on dependencies
- Data flow between tasks
- Optional parameter management
- Result storage and retrieval

### Dependency Resolution
ProcessFlow automatically:
- Analyzes task inputs and outputs
- Builds a dependency graph
- Executes tasks in the correct order
- Handles data passing between tasks

## Use Cases

- **Data Pipeline Processing**: Transform raw data through multiple dependent steps
- **Financial Calculations**: Complex calculations with interdependent metrics
- **Scientific Computing**: Multi-step analyses with clear data lineage
- **ETL Workflows**: Extract, transform, and load operations with dependency management

## Requirements

- Python >= 3.13
- pandas >= 2.3.1
- networkx >= 3.5
- Additional dependencies for visualization and advanced features

## License

[Add your license information here]

## Contributing

[Add contribution guidelines here]
