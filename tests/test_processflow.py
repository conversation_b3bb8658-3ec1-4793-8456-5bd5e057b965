import pandas as pd
import pytest
from processflow.processflow import PandasProcessFlow
from processflow.decorators.calctask import _is_calctask
from processflow.decorators import calctask, calctask_transformer


@calctask(outputs="sum")
def add_columns(col1: pd.Series, col2: pd.Series):
    return col1 + col2


@calctask(outputs="diff")
def subtract_columns(col1: pd.Series, col2: pd.Series):
    return col2 - col1


@calctask(outputs=["mul", "div"])
def calculate_stats(sum: pd.Series, diff: pd.Series, margin: int = 0):
    return sum * diff + margin, (sum / diff).astype(int) - margin


@calctask(outputs="scaled_mul")
def scale_sum(mul: pd.Series, scaler: float | int = 2):
    return mul * scaler


@calctask(outputs="scaled_col1")
def margin_scaled(col1: pd.Series, scaler: int = 1, margin: int = 0):
    return col1 * scaler + margin


def test_calctask_decorator_attributes():
    assert _is_calctask(add_columns)
    assert add_columns.__required_args__ == ["col1", "col2"]
    assert add_columns.__optional_args__ == []
    assert add_columns.__outputs__ == ["sum"]
    assert _is_calctask(scale_sum)
    assert scale_sum.__required_args__ == ["mul"]
    assert scale_sum.__optional_args__ == ["scaler"]
    assert scale_sum.__outputs__ == ["scaled_mul"]


def test_error_on_non_calctask(base_dataframe_input):

    # Raise KeyError if some required arguments are not found in database for `PandasProcessFlow`
    with pytest.raises(KeyError):
        PandasProcessFlow(
            database=base_dataframe_input, calctasks=[calculate_stats]
        ).run()

    def invalid_inputs_calctask(col1: pd.Series):
        return col1 + 1

    def invalid_optional_arg_calctask(constant: int = 42):
        return constant

    def invalid_optional_arg_calctask_ninputs(constant: int):
        return constant

    # Raise ValueError if ninputs is not an integer or is negative
    with pytest.raises(ValueError):
        calctask(outputs="col1_plus_1", ninputs=2.0)(
            invalid_inputs_calctask
        )  # ninputs is not an integer
        calctask(outputs="col1_plus_1", ninputs=-1)(
            invalid_inputs_calctask
        )  # ninputs is negative
        calctask(outputs="constant", ninputs=1)(
            invalid_optional_arg_calctask
        )  # ninputs is specified but function has optional arguments
        calctask(outputs="constant", ninputs=2)(
            invalid_optional_arg_calctask_ninputs
        )  # ninputs greater than number of required arguments


@pytest.mark.parametrize("calctask_func", [add_columns, subtract_columns])
def test_processflow_runs(base_dataframe, base_dataframe_input, calctask_func):
    """Tests calctask 'add_columns' and 'subtract_columns' one-by-one."""
    # 'calctask_func' is now the actual function object
    process_flow = PandasProcessFlow(
        database=base_dataframe_input, calctasks=[calctask_func]
    )
    process_flow.run()
    expected_df = base_dataframe[
        calctask_func.__required_args__ + calctask_func.__outputs__
    ]
    pd.testing.assert_frame_equal(process_flow.database, expected_df)


def test_calctask_with_multiple_outputs(base_dataframe, base_dataframe_input):
    """Tests whole processflow for defined calctasks."""
    # Test whole calctask processflow
    process_flow = PandasProcessFlow(
        database=base_dataframe_input,
        calctasks=[add_columns, subtract_columns, calculate_stats, scale_sum],
    )
    process_flow.run()
    pd.testing.assert_frame_equal(process_flow.database, base_dataframe)


def test_calctask_with_optionals(base_dataframe, base_dataframe_input):
    """Tests whole processflow with changed optional arguments for defined calctasks."""

    # Define attributes for optional arguments
    margin = 2
    scaler = 1

    # Define expected output based on optional arguments
    base_dataframe_modified = base_dataframe.copy()
    base_dataframe_modified["mul"] += margin
    base_dataframe_modified["div"] -= margin
    base_dataframe_modified["scaled_mul"] = base_dataframe_modified["mul"] * scaler

    # --- Test task-specific optionals ---

    # Define optionals arg for 'PandasProcessFlow' via optionals dict
    optionals = {calculate_stats: {"margin": margin}, "scale_sum": {"scaler": scaler}}
    process_flow = PandasProcessFlow(
        database=base_dataframe_input,
        calctasks=[add_columns, subtract_columns, calculate_stats, scale_sum],
        optionals=optionals,
    )
    process_flow.run()
    pd.testing.assert_frame_equal(process_flow.database, base_dataframe_modified)

    # --- Test global optionals ---

    # Define optionals arg for 'PandasProcessFlow' via kwargs
    process_flow = PandasProcessFlow(
        database=base_dataframe_input,
        calctasks=[add_columns, subtract_columns, calculate_stats, scale_sum],
        margin=margin,
        scaler=scaler,
    )
    process_flow.run()
    pd.testing.assert_frame_equal(process_flow.database, base_dataframe_modified)

    # --- Test task-specific and global optionals ---

    # Create expected column output
    base_dataframe_modified["scaled_col1"] = base_dataframe_modified["col1"] * 2 + 1

    # Define optionals arg for 'PandasProcessFlow' via kwargs and optionals dict
    process_flow = PandasProcessFlow(
        database=base_dataframe_input,
        calctasks=[
            add_columns,
            subtract_columns,
            calculate_stats,
            scale_sum,
            margin_scaled,
        ],
        optionals={
            margin_scaled: {"scaler": 2, "margin": 1}
        },  # Make task-specific optionals
        margin=margin,
        scaler=scaler,
    )
    process_flow.run()

    pd.testing.assert_frame_equal(process_flow.database, base_dataframe_modified)


def test_calctask_transformer(base_dataframe_input: pd.DataFrame):

    meta = {"col1": "diff", "col2": "sum"}
    calculate_stats_t = calctask_transformer(calculate_stats, meta=meta)
    process_flow = PandasProcessFlow(
        database=base_dataframe_input, calctasks=[calculate_stats_t]
    )
    process_flow.run()

    assert (
        list(process_flow.database.columns)
        == list(meta.keys()) + calculate_stats_t.__outputs__
    )

    expected_df = base_dataframe_input.copy()
    expected_df["mul"] = expected_df["col1"] * expected_df["col2"]
    expected_df["div"] = (expected_df["col2"] / expected_df["col1"]).astype(int)

    pd.testing.assert_frame_equal(process_flow.database, expected_df)
