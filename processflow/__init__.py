"""
processflow module initialization

This module provides the main API for the processflow package, including the PandasProcessFlow class
and the decorators for defining calculation tasks.

*** Importing the main classes and decorators: ***

>>> from processflow import calctask, calctask_transformer
>>> from processflow import PandasProcessFlow, PandasProcessFlowGraph
"""

from processflow.processflow import PandasProcessFlow
from processflow.decorators import __all__ as decorator_all
from processflow.build import __all__ as build_all
from processflow._utils import __all__ as utils_all

# --- Define main API for processflow module ---
__all__ = ["PandasProcessFlow"]
__all__ += (
    decorator_all + 
    build_all +
    utils_all
    )
