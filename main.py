if __name__ == "__main__":
    from dataclasses import dataclass
    import pandas as pd
    from processflow.processflow import PandasProcessFlow
    from processflow.decorators import calctask, input_registry_asset
    from processflow.build.graph import PandasProcessFlowGraph

    @calctask("sum")
    def add(a: int, b: int, c: int = 10) -> int:
        return a + b * c

    @calctask(["mul", "div"])
    def mul_div(a: int, sum: int, c: int = 10, d: int = 5) -> tuple[int, float]:
        return (a - c) * sum, a / (sum + d)

    @calctask("result")
    def sub(mul: int, div: float, z: float = 0.5) -> int:
        return (mul - div) * z
    
    @input_registry_asset
    @dataclass
    class Data:
        dummy1: int
        dummy2: int
        dummy3: int

    @calctask("dummy", params_from=Data)
    def dump_dummies(result: int, div: float, dummy1: int, dummy2: int, dummy3: int):
        return result * div - (dummy1 + dummy2) * (dummy1 + dummy2) - dummy3

    df = pd.DataFrame({"a": [1, 2, 3], "b": [4, 5, 6]}, index=["id1", "id2", "id3"])
    optionals = {"add": {"c": 100}, mul_div: {"d": -10}}

    ppf = PandasProcessFlow(
        df, [add, mul_div, sub, dump_dummies], optionals=optionals, process_name="test_process"
    )
    ppf.run()
    print("Process flow run successfully.")

    graph = PandasProcessFlowGraph(ppf)
    g = graph.build()
    g.render("assets/process_flow", format="png", view=False)
    print("Process flow graph created and plotted successfully.")
